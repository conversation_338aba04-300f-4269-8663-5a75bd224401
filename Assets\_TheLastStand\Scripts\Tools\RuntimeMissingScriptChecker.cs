using UnityEngine;

public class RuntimeMissingScriptChecker : MonoBehaviour
{
    [System.Serializable]
    public class MissingScriptInfo
    {
        public string gameObjectName;
        public string gameObjectPath;
        public int missingScriptCount;
    }

    void Start()
    {
        CheckForMissingScripts();
    }

    public void CheckForMissingScripts()
    {
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        int totalMissingScripts = 0;
        
        foreach (GameObject obj in allObjects)
        {
            Component[] components = obj.GetComponents<Component>();
            int missingCount = 0;
            
            foreach (Component component in components)
            {
                if (component == null)
                {
                    missingCount++;
                    totalMissingScripts++;
                }
            }
            
            if (missingCount > 0)
            {
                string path = GetGameObjectPath(obj);
                Debug.LogError($"Missing Script Found: GameObject '{obj.name}' at path '{path}' has {missingCount} missing script(s)", obj);
            }
        }
        
        if (totalMissingScripts == 0)
        {
            Debug.Log("No missing scripts found in the scene.");
        }
        else
        {
            Debug.LogError($"Total missing scripts found: {totalMissingScripts}");
        }
    }
    
    private string GetGameObjectPath(GameObject obj)
    {
        string path = obj.name;
        Transform parent = obj.transform.parent;
        
        while (parent != null)
        {
            path = parent.name + "/" + path;
            parent = parent.parent;
        }
        
        return path;
    }
}