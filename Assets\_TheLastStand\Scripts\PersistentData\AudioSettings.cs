using UnityEngine;
using System.Collections;

public class AudioSettings : MonoBehaviour
{
    public static AudioSettings Instance { get; private set; }

    public float masterVolume = 100f;
    public float musicVolume = 40f;
    public float soundFxVolume = 100f;
    public float dialogueVolume = 100f;
    public float enemyVolume = 100f;
    public float ambienceVolume = 75f;

    private const string MasterVolumeKey = "MasterVolume";
    private const string MusicVolumeKey = "MusicVolume";
    private const string SoundFxVolumeKey = "SoundFxVolume";
    private const string DialogueVolumeKey = "DialogueVolume";
    private const string EnemyVolumeKey = "EnemyVolume";
    private const string AmbienceVolumeKey = "AmbienceVolume";

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;

            // Ensure this GameObject is a root GameObject before calling DontDestroyOnLoad
            if (transform.parent != null)
            {
                Debug.LogWarning("AudioSettings: GameObject is not a root GameObject. Moving to root before applying DontDestroyOnLoad.", this);
                transform.SetParent(null);
            }

            // Validate that the GameObject is properly set up before applying DontDestroyOnLoad
            if (gameObject != null && transform.parent == null)
            {
                DontDestroyOnLoad(gameObject);
                LoadSettings();
                Debug.Log("AudioSettings: Successfully initialized and set to DontDestroyOnLoad.");
            }
            else
            {
                Debug.LogError("AudioSettings: Failed to properly initialize DontDestroyOnLoad. GameObject or transform is invalid.", this);
            }
        }
        else
        {
            Debug.Log("AudioSettings: Instance already exists. Destroying duplicate.", this);
            Destroy(gameObject);
        }
    }

    public void SetMasterVolume(float volume)
    {
        Debug.Log($"AudioSettings: SetMasterVolume received slider value: {volume}. Current masterVolume before clamp: {this.masterVolume}");
        masterVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: masterVolume after clamp: {this.masterVolume}");
        SaveSettings();
    }

    public void SetMusicVolume(float volume)
    {
        Debug.Log($"AudioSettings: SetMusicVolume received slider value: {volume}. Current musicVolume before clamp: {this.musicVolume}");
        musicVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: musicVolume after clamp: {this.musicVolume}");
        SaveSettings();
    }

    public void SetSoundFxVolume(float volume)
    {
        Debug.Log($"AudioSettings: SetSoundFxVolume received slider value: {volume}. Current soundFxVolume before clamp: {this.soundFxVolume}");
        soundFxVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: soundFxVolume after clamp: {this.soundFxVolume}");
        SaveSettings();
    }

    public void SetDialogueVolume(float volume)
    {
        Debug.Log($"AudioSettings: SetDialogueVolume received slider value: {volume}. Current dialogueVolume before clamp: {this.dialogueVolume}");
        dialogueVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: dialogueVolume after clamp: {this.dialogueVolume}");
        SaveSettings();
    }

    public void SetEnemyVolume(float volume)
    {
        Debug.Log($"AudioSettings: SetEnemyVolume received slider value: {volume}. Current enemyVolume before clamp: {this.enemyVolume}");
        enemyVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: enemyVolume after clamp: {this.enemyVolume}");
        SaveSettings();
    }

    public void SetAmbienceVolume(float volume) // Added SetAmbienceVolume
    {
        Debug.Log($"AudioSettings: SetAmbienceVolume received slider value: {volume}. Current ambienceVolume before clamp: {this.ambienceVolume}");
        ambienceVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: ambienceVolume after clamp: {this.ambienceVolume}");
        SaveSettings();
    }

    public void SaveSettings()
    {
        PlayerPrefs.SetFloat(MasterVolumeKey, masterVolume);
        PlayerPrefs.SetFloat(MusicVolumeKey, musicVolume);
        PlayerPrefs.SetFloat(SoundFxVolumeKey, soundFxVolume);
        PlayerPrefs.SetFloat(DialogueVolumeKey, dialogueVolume);
        PlayerPrefs.SetFloat(EnemyVolumeKey, enemyVolume);
        PlayerPrefs.SetFloat(AmbienceVolumeKey, ambienceVolume); // Save Ambience Volume
        PlayerPrefs.Save();
        Debug.Log("AudioSettings: Settings Saved.");
    }

    public void LoadSettings()
    {
        masterVolume = PlayerPrefs.GetFloat(MasterVolumeKey, 100f);
        musicVolume = PlayerPrefs.GetFloat(MusicVolumeKey, 100f);
        soundFxVolume = PlayerPrefs.GetFloat(SoundFxVolumeKey, 100f);
        dialogueVolume = PlayerPrefs.GetFloat(DialogueVolumeKey, 100f);
        enemyVolume = PlayerPrefs.GetFloat(EnemyVolumeKey, 100f);
        ambienceVolume = PlayerPrefs.GetFloat(AmbienceVolumeKey, 75f); // Load Ambience Volume
        Debug.Log("AudioSettings: Settings Loaded.");
    }

    // Optional: Call SaveSettings() when the application quits to ensure data is saved.
    private void OnApplicationQuit()
    {
        SaveSettings();
    }

    public void FadeOutSound(AudioSource audioSource, float fadeTime)
    {
        StartCoroutine(FadeOutCoroutine(audioSource, fadeTime));
    }

    private IEnumerator FadeOutCoroutine(AudioSource audioSource, float fadeTime)
    {
        float startVolume = audioSource.volume;
        while (audioSource.volume > 0)
        {
            audioSource.volume -= startVolume * Time.deltaTime / fadeTime;
            yield return null;
        }
        audioSource.Stop();
    }
    
}
