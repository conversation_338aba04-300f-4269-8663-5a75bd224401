using UnityEngine;

public class ForestIntroPlayer : MonoBehaviour
{
    [Header("References")]
    [Tooltip("The transform of the player's visual model. Should be a child of this object.")]
    [SerializeField] private Transform playerModel;
    private Transform cameraTransform;
    private ForestMap_UIManager uiManager;

    [Header("Settings")]
    [SerializeField] private float mouseSensitivity = 200f;
    [SerializeField] private float verticalLookMinAngle = -85f;
    [SerializeField] private float verticalLookMaxAngle = 85f;

    private float _currentCameraXRotation = 0f;
    private readonly Vector3 _cameraLocalOffset = new Vector3(0f, 1.693f, 0.174f);
    private readonly Quaternion _cameraLocalRotation = Quaternion.identity;
    private bool isSettingsPanelOpen = false;

    void Start()
    {
        LockCursor();

        if (playerModel == null)
        {
            Debug.LogWarning("ForestIntroPlayer: 'playerModel' is not assigned. Visual Z-axis rotation based on yaw will not apply to a separate model.", this);
        }

        // Try to find UI Manager, but handle gracefully if not found
        uiManager = FindObjectOfType<ForestMap_UIManager>();
        if (uiManager == null)
        {
            Debug.LogWarning("ForestIntroPlayer: ForestMap_UIManager not found in scene. ESC to toggle settings panel will not work. This is expected in MainMenu scene.", this);
        }
        else
        {
            Debug.Log("ForestIntroPlayer: Successfully found ForestMap_UIManager in scene.", this);
        }
    }

    public void InitializePlayerCamera(Camera cameraToUse)
    {
        if (cameraToUse != null)
        {
            Debug.Log($"ForestIntroPlayer: Configuring assigned camera '{cameraToUse.name}'.", this);
            cameraToUse.transform.SetParent(this.transform);
            cameraToUse.transform.localPosition = _cameraLocalOffset;
            cameraToUse.transform.localRotation = _cameraLocalRotation;
            this.cameraTransform = cameraToUse.transform;
        }
        else
        {
            Debug.LogWarning("ForestIntroPlayer: No specific camera assigned by Manager. Attempting to find and configure Camera.main.", this);
            Camera mainCam = Camera.main;
            if (mainCam != null)
            {
                Debug.Log($"ForestIntroPlayer: Found and configuring Camera.main '{mainCam.name}'.", this);
                mainCam.transform.SetParent(this.transform);
                mainCam.transform.localPosition = _cameraLocalOffset;
                mainCam.transform.localRotation = _cameraLocalRotation;
                this.cameraTransform = mainCam.transform;
            }
            else
            {
                Debug.LogError("ForestIntroPlayer: No camera provided and Camera.main could not be found. Player will not have a camera managed by this script.", this);
            }
        }
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            TogglePanelAndCursor();
        }

        if (!isSettingsPanelOpen)
        {
            HandleMouseLook();
        }
    }

    private void TogglePanelAndCursor()
    {
        if (uiManager != null)
        {
            uiManager.ToggleSettingsPanel();
            isSettingsPanelOpen = !isSettingsPanelOpen;

            if (isSettingsPanelOpen)
            {
                UnlockCursor();
            }
            else
            {
                LockCursor();
            }
        }
        else
        {
            Debug.LogWarning("ForestIntroPlayer: uiManager reference is null. Cannot toggle settings panel.", this);
        }
    }

    private void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity * Time.deltaTime;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity * Time.deltaTime;

        transform.Rotate(Vector3.up * mouseX);

        if (cameraTransform != null)
        {
            _currentCameraXRotation -= mouseY;
            _currentCameraXRotation = Mathf.Clamp(_currentCameraXRotation, verticalLookMinAngle, verticalLookMaxAngle);
            cameraTransform.localRotation = Quaternion.Euler(_currentCameraXRotation, 0f, 0f);
        }
    }

    void LateUpdate()
    {
        if (playerModel != null && playerModel != this.transform)
        {
            float yawAngle = transform.eulerAngles.y;
            playerModel.rotation = Quaternion.Euler(0f, 0f, yawAngle);
        }
    }

    public float GetWorldZRotation()
    {
        return transform.eulerAngles.y;
    }

    public Quaternion GetWorldOrientation()
    {
        return transform.rotation;
    }

    public void LockCursor()
    {
        Cursor.lockState = CursorLockMode.Unlocked;
        Cursor.visible = true;
    }

    public void UnlockCursor()
    {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }
}
