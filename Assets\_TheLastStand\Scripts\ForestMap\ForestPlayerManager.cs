using UnityEngine;
using System.Collections.Generic;

public class ForestPlayerManager : MonoBehaviour
{
    [Header("Prefabs")]
    [SerializeField] private GameObject clientPlayerPrefab;
    [SerializeField] private GameObject replicatedPlayerPrefabOne;
    [SerializeField] private GameObject replicatedPlayerPrefabTwo;
    [SerializeField] private GameObject replicatedPlayerPrefabThree;
    [Header("Scene References")]
    [SerializeField] private Camera playerCameraToAssign;

    [Header("Spawn Points")]
    [SerializeField] private Transform localPlayerSpawnPoint;
    [SerializeField] private List<Transform> replicatedPlayerSpawnPoints = new List<Transform>();

    private ForestIntroPlayer _localPlayerInstance;
    private List<ForestIntroReplicatedPlayer> _replicatedPlayerInstances = new List<ForestIntroReplicatedPlayer>();

    void Start()
    {
        SpawnLocalPlayer();
        SpawnReplicatedPlayers();
    }

    void SpawnLocalPlayer()
    {
        if (clientPlayerPrefab == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'clientPlayerPrefab' is not assigned.", this);
            return;
        }
        if (localPlayerSpawnPoint == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'localPlayerSpawnPoint' is not assigned. Cannot spawn local player.", this);
            return;
        }

        GameObject localPlayerGO = Instantiate(clientPlayerPrefab, localPlayerSpawnPoint.position, localPlayerSpawnPoint.rotation, localPlayerSpawnPoint);
        _localPlayerInstance = localPlayerGO.GetComponent<ForestIntroPlayer>();

        if (_localPlayerInstance == null)
        {
            Debug.LogError("ForestIntroPlayerManager: The 'clientPlayerPrefab' does not have a ForestIntroPlayer component.", localPlayerGO);
            Destroy(localPlayerGO); // Cleanup
        }
        else
        {
            localPlayerGO.name = "Local_ForestIntroPlayer";
            if (playerCameraToAssign != null)
            {
                _localPlayerInstance.InitializePlayerCamera(playerCameraToAssign);
            }
            else
            {
                Debug.LogWarning("ForestPlayerManager: 'playerCameraToAssign' is not assigned in the Inspector. Local player may use Camera.main or have no camera.", this);
                _localPlayerInstance.InitializePlayerCamera(null); // Call with null to allow fallback in player script
            }
        }
    }

    void SpawnReplicatedPlayers()
    {
        List<GameObject> availableReplicatedPrefabs = new List<GameObject>();
        if (replicatedPlayerPrefabOne != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabOne);
        if (replicatedPlayerPrefabTwo != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabTwo);
        if (replicatedPlayerPrefabThree != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabThree);

        if (availableReplicatedPrefabs.Count == 0)
        {
            Debug.LogError("ForestPlayerManager: No replicated player prefabs assigned (One, Two, or Three). Cannot spawn replicated players.", this);
            return;
        }

        if (replicatedPlayerSpawnPoints.Count == 0)
        {
            Debug.LogWarning("ForestPlayerManager: No spawn points provided for replicated players.", this);
            return;
        }

        int numberOfPlayersToSpawn = Mathf.Min(replicatedPlayerSpawnPoints.Count, availableReplicatedPrefabs.Count);

        if (replicatedPlayerSpawnPoints.Count > availableReplicatedPrefabs.Count)
        {
            Debug.LogWarning($"ForestPlayerManager: More spawn points ({replicatedPlayerSpawnPoints.Count}) than available unique replicated prefabs ({availableReplicatedPrefabs.Count}). Only spawning {availableReplicatedPrefabs.Count} replicated players.", this);
        }

        for (int i = 0; i < numberOfPlayersToSpawn; i++)
        {
            Transform spawnPoint = replicatedPlayerSpawnPoints[i];
            GameObject prefabToSpawn = availableReplicatedPrefabs[i];

            if (spawnPoint == null)
            {
                Debug.LogWarning($"ForestPlayerManager: Replicated player spawn point at index {i} is null. Skipping.", this);
                continue;
            }

            if (prefabToSpawn == null)
            {
                Debug.LogWarning($"ForestPlayerManager: Replicated player prefab for index {i} is unexpectedly null. Skipping.", this);
                continue;
            }

            // Player 1 (Host) - no special rotation here.
            // Player 2 (Replicated Index 0) - spawns with default spawn point rotation.
            // Player 3 (Replicated Index 1) - needs 180 Y rotation.
            // Player 4 (Replicated Index 2) - needs 180 Y rotation.
            // Instantiate with spawn point's initial rotation first.
            GameObject replicatedPlayerGO = Instantiate(prefabToSpawn, spawnPoint.position, spawnPoint.rotation, spawnPoint);
            ForestIntroReplicatedPlayer replicatedPlayer = replicatedPlayerGO.GetComponent<ForestIntroReplicatedPlayer>();

            if (replicatedPlayer == null)
            {
                Debug.LogError($"ForestPlayerManager: The prefab \"{prefabToSpawn.name}\" does not have a ForestIntroReplicatedPlayer component. Spawned for index {i}.", replicatedPlayerGO);
                Destroy(replicatedPlayerGO); // Cleanup
            }
            else
            {
                replicatedPlayerGO.name = $"Replicated_ForestIntroPlayer_{i}_{prefabToSpawn.name}";
                _replicatedPlayerInstances.Add(replicatedPlayer);

                // Set initial Y rotation offset for Player 3 (index 1) and Player 4 (index 2)
                if (i == 1 || i == 2)
                {
                    replicatedPlayer.SetInitialYRotationOffset(180f);
                }
            }
        }
        
        clientPlayerPrefab.GetComponent<ForestIntroPlayer>().LockCursor();
    }

    void Update()
    {
        // This section simulates network updates.
        // In a real multiplayer game, data would be sent and received over a network.

        if (_localPlayerInstance != null && _replicatedPlayerInstances.Count > 0)
        {
            // 1. Get the Z rotation from the local client player.
            // Using GetWorldOrientation() and then taking .eulerAngles.z to be consistent
            // with the replicated player potentially needing only the Z component of a full quaternion.
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldZRotation = localPlayerOrientation.eulerAngles.z;

            // 2. "Send" this Z rotation to all replicated players.
            foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)
            {
                if (replicatedPlayer != null)
                {
                    // Here we directly call the method that uses the Z rotation.
                    // The Replicated Player script itself ensures only its Z world axis is changed.
                    replicatedPlayer.SetVisualWorldZRotation(localPlayerWorldZRotation);
                }
            }
        }
    }
}
