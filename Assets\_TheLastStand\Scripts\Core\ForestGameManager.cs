using UnityEngine;

public class ForestGameManager : GameManager
{
    
    [SerializeField] private ForestIntroHelicopter forestIntroHelicopter;
    [SerializeField] private ForestMap_AudioManager forestMapAudioManager;

    public void Start()
    {
      if (forestIntroHelicopter != null)
      {
        forestIntroHelicopter.Invoke("StartIntroSequence", 1f);
      }
      else
      {
        Debug.LogError("ForestGameManager: ForestIntroHelicopter not assigned in the Inspector.");
      }

      if (forestMapAudioManager != null)
      {
          forestMapAudioManager.PlayIntroStormSound();
          forestMapAudioManager.PlayIntroBackgroundMusic();
          forestMapAudioManager.Invoke("PlayNarratorIntro", 2f);
      }
      else
      {
          Debug.LogError("ForestGameManager: ForestMapAudioManager not assigned in the Inspector.");
      }
    }

    public void OnHelicopterLanded()
    {
      // Fade out the helicopter sound
      AudioSettings.Instance.FadeOutSound(forestIntroHelicopter.GetComponent<AudioSource>(), 2f);

      
    }
    
}
