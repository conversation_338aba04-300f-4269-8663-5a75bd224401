using Mirror;
using Steamworks;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;

[System.Serializable]
public struct PlayerInfoData 
{
    public string username;
    public ulong steamId;

    public PlayerInfoData(string username, ulong steamId)
    {
        this.username = username;
        this.steamId = steamId;
    }
}
public class MyClient : NetworkBehaviour
{
    [SyncVar(hook = nameof(PlayerInfoUpdate))]
    public PlayerInfoData playerInfo;

    [SyncVar(hook = nameof(IsReadyUpdate))]
    public bool IsReady;

    [SyncVar(hook = nameof(OnAvatarDataChanged))]
    public byte[] avatarData;

    public event Action OnClientUpdated;

    [Header("Controller")]
    [SerializeField] private GameObject controllerObj;
    [SerializeField] private GameObject meshObj;
    [SerializeField] private GameObject camHolder;
    [SerializeField] private Behaviour[] controllerComponents;

    public Sprite icon { get; private set; }
    public CharacterSkinElement characterInstance { get; set; }

    #region Steam PFP
    protected Callback<AvatarImageLoaded_t> avatarImageLoaded;
    private void OnAvatarImageLoaded(AvatarImageLoaded_t callback)
    {
        if (callback.m_steamID.m_SteamID != playerInfo.steamId || playerInfo.steamId == 0)
        {
            return;
        }
        
        if (avatarData == null || avatarData.Length == 0) 
        {
            CSteamID steamId = new CSteamID(playerInfo.steamId);
            Texture2D tex = SteamHelper.GetAvatar(steamId); 
            if (tex)
            {
                icon = SteamHelper.ConvertTextureToSprite(tex);
                OnClientUpdated?.Invoke(); 
            }
        }
    }

    void SetIconFromSteamID(CSteamID steamId)
    {
        if (steamId == CSteamID.Nil || !steamId.IsValid()) {
            icon = null;
            return;
        }

        Texture2D tex = SteamHelper.GetAvatar(steamId);
        if (tex)
        {
            icon = SteamHelper.ConvertTextureToSprite(tex);
        }
        else
        {
            icon = null;
        }
    }

    private void OnAvatarDataChanged(byte[] oldData, byte[] newData)
    {
        // Only process if data actually changed
        if (oldData == newData || (oldData != null && newData != null && oldData.Length == newData.Length && oldData.Zip(newData, (a, b) => a == b).All(x => x)))
            return;

        if (newData != null && newData.Length > 0)
        {
            Texture2D tex = new Texture2D(2, 2);
            if (tex.LoadImage(newData))
            {
                icon = SteamHelper.ConvertTextureToSprite(tex);
            }
            else
            {
                icon = null;
            }
        }
        else
        {
            icon = null;
        }
        OnClientUpdated?.Invoke();
    }
    #endregion

    private void Start()
    {
        if(CharacterSkinHandler.instance) CharacterSkinHandler.instance.SpawnCharacterMesh(this);
        avatarImageLoaded = Callback<AvatarImageLoaded_t>.Create(OnAvatarImageLoaded);

        if (SceneManager.GetActiveScene().name != "MainMenu")
        {
            ToggleController(true);
        }
        else
        { 
            ToggleController(false);
        }
    }

    void ToggleController(bool value) 
    {
        controllerObj.SetActive(value);
        meshObj.SetActive(value ? !isLocalPlayer : false);
        camHolder.SetActive(value ? isLocalPlayer : false);

        if (!isLocalPlayer) 
            value = false;

        
        foreach (var component in controllerComponents)
        {
            component.enabled = value;
        }

        GetComponent<CharacterController>().enabled = value;
    }

    #region Ready Up
    public void ToggleReady() => Cmd_ToggleReady();

    [Command]
    private void Cmd_ToggleReady()
    {
        NetworkPerformanceMonitor.LogRPC();
        IsReady = !IsReady;
    }
    #endregion

    #region SyncVar Hooks
    private void PlayerInfoUpdate(PlayerInfoData oldData, PlayerInfoData newData)
    {
        // Only update if data actually changed
        if (oldData.Equals(newData))
            return;

        NetworkPerformanceMonitor.LogSyncVarUpdate();

        if (characterInstance)
            characterInstance.Initialize(this, IsReady);

        OnClientUpdated?.Invoke();
    }

    public void IsReadyUpdate(bool oldValue, bool newValue)
    {
        // Only update if value actually changed
        if (oldValue == newValue)
            return;

        NetworkPerformanceMonitor.LogSyncVarUpdate();

        if (characterInstance)
            characterInstance.Initialize(this, newValue);

        if (isLocalPlayer)
        {
            if(MainMenu.instance != null) MainMenu.instance.UpdateReadyButton(newValue);
        }

        OnClientUpdated?.Invoke();
    }

    #endregion

    private void OnDestroy()
    {
        if (characterInstance && !isLocalPlayer)
        {
            CharacterSkinHandler.instance.DestroyCharacterMesh(this);
        }
    }
}
