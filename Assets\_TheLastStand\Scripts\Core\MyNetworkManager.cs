using Mirror;
using Steamworks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MyNetworkManager : NetworkManager
{
    public static bool isMultiplayer;
    public static MyNetworkManager instance; // Static instance

    public override void Awake()
    {
        base.Awake();
        if (instance == null) 
        {
            instance = this;
        }
        else 
        {
            Destroy(gameObject); 
            return;
        }
        EnsureLobbyPlayerListExists();
    }

    public override void OnStartServer()
    {
        base.OnStartServer();
        EnsureLobbyPlayerListExists();
    }

    private void EnsureLobbyPlayerListExists()
    {
        if (LobbyPlayerList.instance == null)
        {
            LobbyPlayerList listComponent = GetComponent<LobbyPlayerList>();
            if (listComponent == null)
            {
                Debug.LogWarning("MyNetworkManager: LobbyPlayerList component not found on NetworkManager GameObject. Please add it in the Editor, and ensure a NetworkIdentity is present.");
            }
            
            if (LobbyPlayerList.instance == null && listComponent != null)
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance was set by finding component.");
            }
            else if (LobbyPlayerList.instance != null)
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance is available.");
            }
            else
            {
                Debug.LogError("MyNetworkManager: Failed to find LobbyPlayerList.instance. Critical error.");
            }
        }
    }

    public override void OnServerAddPlayer(NetworkConnectionToClient conn)
    {
        // Validate spawn position before calling base method to prevent NavMesh errors
        if (playerPrefab != null && GetStartPosition() != null)
        {
            Vector3 spawnPosition = GetStartPosition().position;

            // Check if spawn position is on NavMesh and adjust if necessary
            UnityEngine.AI.NavMeshHit hit;
            if (!UnityEngine.AI.NavMesh.SamplePosition(spawnPosition, out hit, 5.0f, UnityEngine.AI.NavMesh.AllAreas))
            {
                Debug.LogWarning($"MyNetworkManager: Spawn position {spawnPosition} is not on NavMesh. Attempting to find nearest valid position.");

                // Try to find a valid position within a larger radius
                if (UnityEngine.AI.NavMesh.SamplePosition(spawnPosition, out hit, 20.0f, UnityEngine.AI.NavMesh.AllAreas))
                {
                    Debug.Log($"MyNetworkManager: Found valid NavMesh position at {hit.position} for spawn.");
                    // Update the spawn position temporarily
                    Transform startPos = GetStartPosition();
                    Vector3 originalPos = startPos.position;
                    startPos.position = hit.position;

                    try
                    {
                        base.OnServerAddPlayer(conn);
                    }
                    finally
                    {
                        // Restore original position
                        startPos.position = originalPos;
                    }
                }
                else
                {
                    Debug.LogError("MyNetworkManager: Could not find valid NavMesh position for player spawn. Using original position.");
                    base.OnServerAddPlayer(conn);
                }
            }
            else
            {
                base.OnServerAddPlayer(conn);
            }
        }
        else
        {
            base.OnServerAddPlayer(conn);
        }

        // Validate client component exists before proceeding
        if (conn.identity != null)
        {
            MyClient client = conn.identity.GetComponent<MyClient>();
            if (client != null)
            {
                InitializePlayerClient(conn, client);

                if (LobbyPlayerList.instance != null)
                {
                    LobbyPlayerList.instance.allClients.Add(client);
                }
                else
                {
                    Debug.LogError("MyNetworkManager: LobbyPlayerList.instance is null. Cannot add client to list.");
                }
            }
            else
            {
                Debug.LogError("MyNetworkManager: MyClient component not found on spawned player object.", conn.identity.gameObject);
            }
        }
        else
        {
            Debug.LogError("MyNetworkManager: Connection identity is null during OnServerAddPlayer.");
        }
    }

    private void InitializePlayerClient(NetworkConnectionToClient conn, MyClient client)
    {
        if (client == null)
        {
            Debug.LogError("MyNetworkManager: Cannot initialize null client.");
            return;
        }

        if (conn == null)
        {
            Debug.LogError("MyNetworkManager: Cannot initialize client with null connection.");
            return;
        }

        try
        {
            CSteamID steamId = GetSteamIDForConnection(conn);
            SetPlayerInfoAndAvatar(client, steamId, conn);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"MyNetworkManager: Exception during client initialization: {ex.Message}");

            // Fallback initialization with basic data
            string fallbackName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
            client.playerInfo = new PlayerInfoData(fallbackName, 0);
            client.avatarData = null;
        }
    }

    private CSteamID GetSteamIDForConnection(NetworkConnectionToClient conn)
    {
        if (conn == NetworkServer.localConnection)
        {
            return SteamUser.GetSteamID();
        }

        if (conn.authenticationData is CSteamID authSteamId && authSteamId.IsValid())
        {
            return authSteamId;
        }

        if (conn.authenticationData is ulong authUlongSteamIdValue && authUlongSteamIdValue != 0)
        {
            return new CSteamID(authUlongSteamIdValue);
        }
        
        // Try to get Steam ID from lobby members if available
        if (SteamLobby.LobbyID.IsValid() && SteamLobby.LobbyID.m_SteamID != 0)
        {
            int numLobbyMembers = SteamMatchmaking.GetNumLobbyMembers(SteamLobby.LobbyID);
            for (int i = 0; i < numLobbyMembers; i++)
            {
                CSteamID memberSteamId = SteamMatchmaking.GetLobbyMemberByIndex(SteamLobby.LobbyID, i);
                if (memberSteamId.IsValid() && memberSteamId != SteamUser.GetSteamID())
                {
                    // This is a basic approach - in a real implementation you'd want to match
                    // the connection to the specific Steam ID more precisely
                    return memberSteamId;
                }
            }
        }
        
        return CSteamID.Nil;
    }

    private void SetPlayerInfoAndAvatar(MyClient client, CSteamID steamId, NetworkConnectionToClient conn)
    {
        if (client == null)
        {
            Debug.LogError("MyNetworkManager: Cannot set player info on null client.");
            return;
        }

        if (conn == null)
        {
            Debug.LogError("MyNetworkManager: Cannot set player info with null connection.");
            return;
        }

        try
        {
            if (steamId.IsValid() && steamId != CSteamID.Nil)
            {
                string personaName = SteamFriends.GetFriendPersonaName(steamId);

                // Validate persona name before creating PlayerInfoData
                if (string.IsNullOrEmpty(personaName))
                {
                    personaName = "Steam User " + steamId.m_SteamID.ToString();
                    Debug.LogWarning($"MyNetworkManager: Empty persona name for Steam ID {steamId.m_SteamID}. Using fallback name: {personaName}");
                }

                // Create PlayerInfoData with validated data
                PlayerInfoData playerInfoData = new PlayerInfoData(personaName, steamId.m_SteamID);
                client.playerInfo = playerInfoData;

                Debug.Log($"MyNetworkManager: Set player info for Steam user: {personaName} (ID: {steamId.m_SteamID})");

                // Handle avatar data safely
                try
                {
                    Texture2D avatarTexture = SteamHelper.GetAvatar(steamId);
                    if (avatarTexture != null)
                    {
                        client.avatarData = avatarTexture.EncodeToPNG();
                        Destroy(avatarTexture);
                    }
                    else
                    {
                        client.avatarData = null;
                    }
                }
                catch (System.Exception avatarEx)
                {
                    Debug.LogWarning($"MyNetworkManager: Failed to get avatar for Steam ID {steamId.m_SteamID}: {avatarEx.Message}");
                    client.avatarData = null;
                }
            }
            else
            {
                string playerName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
                PlayerInfoData playerInfoData = new PlayerInfoData(playerName, 0);
                client.playerInfo = playerInfoData;
                client.avatarData = null;

                Debug.Log($"MyNetworkManager: Set player info for non-Steam user: {playerName}");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"MyNetworkManager: Exception in SetPlayerInfoAndAvatar: {ex.Message}");

            // Emergency fallback
            string fallbackName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
            PlayerInfoData fallbackData = new PlayerInfoData(fallbackName, 0);
            client.playerInfo = fallbackData;
            client.avatarData = null;
        }
    }

    public override void OnServerDisconnect(NetworkConnectionToClient conn)
    {
        if (conn.identity != null)
        {
            MyClient client = conn.identity.GetComponent<MyClient>();
            if (client != null && LobbyPlayerList.instance != null)
            {
                LobbyPlayerList.instance.allClients.Remove(client);
            }
            else if (LobbyPlayerList.instance == null)
            {
                 Debug.LogError("LobbyPlayerList.instance is null during OnServerDisconnect. Cannot remove client from list.");
            }
        }
        base.OnServerDisconnect(conn);
    }

    public override void OnStartClient()
    {
        if (isMultiplayer)
        {
            MainMenu.instance.SetMenuState(MenuState.InParty);
            PopupManager.instance.Popup_Close();
        }

        base.OnStartClient();
    }

    public override void OnStopClient()
    {
        if (isMultiplayer)
        {
            if (LobbyPlayerList.instance != null && NetworkClient.active) // Check if client is active before clearing
            {
                Debug.Log("MyNetworkManager: OnStopClient - Clearing LobbyPlayerList.instance");
                LobbyPlayerList.instance.allClients.Clear();
            }
            MainMenu.instance.SetMenuState(MenuState.Home);
        }

        base.OnStopClient();
    }

    public override void OnStopServer()
    {
        if (LobbyPlayerList.instance != null)
        {
            LobbyPlayerList.instance.allClients.Clear();
        }
        base.OnStopServer();
    }

    public void SetMultiplayer(bool value)
    {
        isMultiplayer = value;

        if (isMultiplayer)
            NetworkServer.dontListen = false;
        else
            NetworkServer.dontListen = true;
    }
}